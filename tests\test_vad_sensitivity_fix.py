"""
🎯 VAD SENSITIVITY FIX TEST
===========================

Quick test to verify the VAD sensitivity fixes work correctly.
"""

import asyncio
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.audio.noise_suppressor import get_noise_suppressor
from utils.audio_utils import enhanced_vad_check
from core.config.interrupt_config import get_interrupt_config


def create_test_signals():
    """Create test signals with different characteristics"""
    sample_rate = 16000
    duration = 1.0
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 1. Pure silence (should NOT trigger VAD)
    silence = np.zeros(len(t), dtype=np.int16)
    
    # 2. Very low background noise (should NOT trigger VAD)
    low_noise = (np.random.normal(0, 0.05, len(t)) * 200).astype(np.int16)
    
    # 3. Medium background noise (should NOT trigger VAD)
    medium_noise = (np.random.normal(0, 0.1, len(t)) * 500).astype(np.int16)
    
    # 4. Realistic human speech (SHOULD trigger VAD)
    speech = (
        np.sin(2 * np.pi * 300 * t) +  # 300 Hz fundamental
        0.7 * np.sin(2 * np.pi * 600 * t) +  # 600 Hz harmonic
        0.5 * np.sin(2 * np.pi * 1200 * t) +  # 1200 Hz harmonic
        0.1 * np.random.normal(0, 1, len(t))  # Natural variation
    )
    human_speech = (speech * 3000).astype(np.int16)  # Realistic amplitude
    
    # 5. Very loud human speech (SHOULD trigger VAD)
    loud_speech = (speech * 8000).astype(np.int16)
    
    return {
        "silence": silence,
        "low_noise": low_noise,
        "medium_noise": medium_noise,
        "human_speech": human_speech,
        "loud_speech": loud_speech
    }


async def test_vad_sensitivity():
    """Test VAD sensitivity with different signals"""
    print("🎯 VAD SENSITIVITY FIX TEST")
    print("=" * 50)
    
    # Check configuration
    config = get_interrupt_config()
    print(f"📋 Current VAD threshold: {config.global_settings.vad_threshold}")
    print()
    
    # Get noise suppressor
    noise_suppressor = get_noise_suppressor("sensitivity_test")
    
    # Create test signals
    test_signals = create_test_signals()
    sample_rate = 16000
    
    print("🧪 Testing VAD sensitivity...")
    print()
    
    expected_results = {
        "silence": False,
        "low_noise": False,
        "medium_noise": False,
        "human_speech": True,
        "loud_speech": True
    }
    
    results = []
    
    for signal_name, audio_samples in test_signals.items():
        # Process through noise suppressor
        filtered_audio, suppression_info = await noise_suppressor.process_audio_async(
            audio_samples, sample_rate
        )
        
        # Apply enhanced VAD
        vad_result = enhanced_vad_check(
            filtered_audio, 
            sample_rate, 
            config.global_settings.vad_threshold
        )
        
        has_voice = vad_result.get('has_voice', False)
        expected = expected_results[signal_name]
        correct = has_voice == expected
        
        energy = vad_result.get('energy', 0)
        zcr = vad_result.get('zero_crossing_rate', 0)
        spectral_centroid = vad_result.get('spectral_centroid', 0)
        
        checks = vad_result.get('checks', {})
        
        results.append({
            "signal": signal_name,
            "has_voice": has_voice,
            "expected": expected,
            "correct": correct,
            "energy": energy,
            "zcr": zcr,
            "spectral_centroid": spectral_centroid,
            "checks": checks
        })
        
        status = "✅" if correct else "❌"
        print(f"{status} {signal_name.upper():<15} | VAD: {has_voice:<5} | Expected: {expected:<5} | Energy: {energy:>8.0f}")
        print(f"   ZCR: {zcr:.3f} | Centroid: {spectral_centroid:>6.1f}Hz")
        
        # Show which checks passed/failed
        if checks:
            check_status = []
            for check_name, check_result in checks.items():
                check_status.append(f"{check_name}: {'✅' if check_result else '❌'}")
            print(f"   Checks: {' | '.join(check_status)}")
        
        print()
    
    # Summary
    correct_count = sum(1 for r in results if r['correct'])
    total_count = len(results)
    
    print("🎯 SUMMARY")
    print("=" * 50)
    print(f"Correct predictions: {correct_count}/{total_count} ({(correct_count/total_count)*100:.1f}%)")
    
    if correct_count == total_count:
        print("🎉 ALL TESTS PASSED! VAD sensitivity is now properly calibrated!")
    else:
        print("⚠️ Some tests failed. VAD sensitivity may need further adjustment.")
        
        # Show failed cases
        failed_cases = [r for r in results if not r['correct']]
        for case in failed_cases:
            print(f"   ❌ {case['signal']}: Expected {case['expected']}, got {case['has_voice']}")
    
    return correct_count == total_count


async def main():
    """Run the VAD sensitivity test"""
    try:
        success = await test_vad_sensitivity()
        if success:
            print("\n✅ VAD sensitivity fixes are working correctly!")
        else:
            print("\n❌ VAD sensitivity still needs adjustment.")
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
