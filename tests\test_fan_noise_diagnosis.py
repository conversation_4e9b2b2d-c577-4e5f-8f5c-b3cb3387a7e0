"""
🎯 FAN NOISE DIAGNOSTIC TEST
============================

Detailed analysis of why fan noise is still being detected as speech.
"""

import asyncio
import numpy as np
import sounddevice as sd
import time
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.audio.noise_suppressor import get_noise_suppressor
from utils.audio_utils import enhanced_vad_check
from core.config.interrupt_config import get_interrupt_config


async def diagnose_fan_noise():
    """Record and analyze fan noise in detail"""
    print("🎯 FAN NOISE DIAGNOSTIC TEST")
    print("=" * 60)
    
    # Get configuration
    config = get_interrupt_config()
    print(f"📋 VAD Threshold: {config.global_settings.vad_threshold}")
    print(f"📋 Noise Reduction: {config.global_settings.enable_noise_reduction}")
    print(f"📋 Noise Strength: {config.global_settings.noise_reduction_strength}")
    print()
    
    # Get noise suppressor
    noise_suppressor = get_noise_suppressor("fan_diagnosis")
    
    print("📋 INSTRUCTIONS:")
    print("   🔇 Stay completely SILENT near your fan")
    print("   🎤 We'll record 5 seconds and analyze in detail")
    print("   🔍 This will show exactly why fan noise triggers VAD")
    
    input("   ⏸️ Press Enter to start recording fan noise...")
    
    # Record 5 seconds of fan noise
    sample_rate = 16000
    duration = 5.0
    print(f"\n🔴 Recording {duration} seconds of fan noise...")
    
    audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
    sd.wait()
    
    audio_samples = audio.flatten()
    print("✅ Recording completed")
    
    # Analyze in chunks (like the recording function does)
    chunk_duration = 0.1  # 100ms chunks (updated to match recording function)
    chunk_size = int(sample_rate * chunk_duration)
    num_chunks = len(audio_samples) // chunk_size

    print(f"\n🔍 DETAILED CHUNK ANALYSIS ({num_chunks} chunks of 100ms each):")
    print("=" * 80)

    speech_chunks = 0
    chunk_results = []

    for i in range(min(num_chunks, 15)):  # Analyze first 15 chunks (1.5 seconds)
        start_idx = i * chunk_size
        end_idx = start_idx + chunk_size
        chunk = audio_samples[start_idx:end_idx]
        
        # Process through noise suppressor
        filtered_chunk, suppression_info = await noise_suppressor.process_audio_async(
            chunk, sample_rate
        )
        
        # Apply VAD
        vad_result = enhanced_vad_check(
            filtered_chunk, 
            sample_rate, 
            config.global_settings.vad_threshold
        )
        
        is_speech = vad_result.get('has_voice', False)
        energy = vad_result.get('energy', 0)
        zcr = vad_result.get('zero_crossing_rate', 0)
        spectral_centroid = vad_result.get('spectral_centroid', 0)
        checks = vad_result.get('checks', {})
        score = vad_result.get('vad_score', 0)
        
        if is_speech:
            speech_chunks += 1
        
        chunk_results.append({
            'chunk': i + 1,
            'is_speech': is_speech,
            'energy': energy,
            'zcr': zcr,
            'spectral_centroid': spectral_centroid,
            'score': score,
            'checks': checks
        })
        
        # Show detailed info for first few chunks
        if i < 10:
            status = "🎤 SPEECH" if is_speech else "🔇 SILENCE"
            print(f"Chunk {i+1:2d}: {status} | Energy: {energy:>8.0f} | ZCR: {zcr:.3f} | "
                  f"Centroid: {spectral_centroid:>6.1f}Hz | Score: {score}/8")
            
            # Show which checks passed/failed
            check_details = []
            for check_name, check_result in checks.items():
                symbol = "✅" if check_result else "❌"
                check_details.append(f"{check_name}: {symbol}")
            print(f"         Checks: {' | '.join(check_details)}")
            print()
    
    # Summary analysis
    speech_percentage = (speech_chunks / min(num_chunks, 15)) * 100
    
    print("🎯 ANALYSIS SUMMARY")
    print("=" * 80)
    print(f"📊 Speech chunks detected: {speech_chunks}/{min(num_chunks, 15)} ({speech_percentage:.1f}%)")
    
    if speech_chunks > 0:
        print("\n❌ PROBLEM: Fan noise is being detected as speech!")
        print("\n🔍 COMMON FAILURE PATTERNS:")
        
        # Analyze why chunks are passing
        failing_checks = {}
        for result in chunk_results:
            if result['is_speech']:
                for check_name, check_result in result['checks'].items():
                    if check_name not in failing_checks:
                        failing_checks[check_name] = {'pass': 0, 'fail': 0}
                    if check_result:
                        failing_checks[check_name]['pass'] += 1
                    else:
                        failing_checks[check_name]['fail'] += 1
        
        for check_name, counts in failing_checks.items():
            total = counts['pass'] + counts['fail']
            pass_rate = (counts['pass'] / total) * 100 if total > 0 else 0
            print(f"   {check_name}: {pass_rate:.1f}% pass rate in speech chunks")
        
        # Suggest fixes
        print("\n💡 SUGGESTED FIXES:")
        
        # Check if energy is the issue
        avg_energy = np.mean([r['energy'] for r in chunk_results if r['is_speech']])
        if avg_energy < config.global_settings.vad_threshold * 50:
            print(f"   📈 Increase VAD threshold (current: {config.global_settings.vad_threshold}, "
                  f"avg speech energy: {avg_energy:.0f})")
        
        # Check if ZCR is the issue
        avg_zcr = np.mean([r['zcr'] for r in chunk_results if r['is_speech']])
        if avg_zcr > 0.3:
            print(f"   🎯 Tighten ZCR range (current allows up to 0.4, "
                  f"fan noise ZCR: {avg_zcr:.3f})")
        
        # Check if spectral centroid is the issue
        avg_centroid = np.mean([r['spectral_centroid'] for r in chunk_results if r['is_speech']])
        if avg_centroid > 3500 or avg_centroid < 300:
            print(f"   🎵 Adjust spectral range (current: 200-4000Hz, "
                  f"fan noise centroid: {avg_centroid:.1f}Hz)")
    
    else:
        print("✅ EXCELLENT: Fan noise is correctly rejected!")
        print("   The VAD system is working properly.")
    
    # Test with full audio analysis
    print(f"\n🎯 FULL AUDIO ANALYSIS ({duration} seconds):")
    print("=" * 80)
    
    # Process full audio
    filtered_full, suppression_info = await noise_suppressor.process_audio_async(
        audio_samples, sample_rate
    )
    
    vad_full = enhanced_vad_check(
        filtered_full, 
        sample_rate, 
        config.global_settings.vad_threshold
    )
    
    print(f"📊 Full audio VAD: {vad_full.get('has_voice', False)}")
    print(f"📊 Full audio energy: {vad_full.get('energy', 0):.0f}")
    print(f"📊 Full audio ZCR: {vad_full.get('zero_crossing_rate', 0):.3f}")
    print(f"📊 Full audio centroid: {vad_full.get('spectral_centroid', 0):.1f}Hz")
    print(f"📊 Full audio score: {vad_full.get('vad_score', 0)}/8")
    
    # Show full audio checks
    full_checks = vad_full.get('checks', {})
    print("📊 Full audio checks:")
    for check_name, check_result in full_checks.items():
        symbol = "✅" if check_result else "❌"
        print(f"   {check_name}: {symbol}")
    
    print("\n" + "=" * 80)
    
    if vad_full.get('has_voice', False):
        print("❌ CONCLUSION: Fan noise is still being detected as speech")
        print("   The VAD system needs further tuning for your specific environment")
    else:
        print("✅ CONCLUSION: Fan noise is correctly rejected in full audio analysis")
        print("   The issue may be with chunk-based processing during recording")


async def main():
    """Run the fan noise diagnostic"""
    try:
        await diagnose_fan_noise()
    except Exception as e:
        print(f"❌ Diagnostic failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
