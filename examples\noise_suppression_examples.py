"""
🎯 MODULAR NOISE SUPPRESSION EXAMPLES
=====================================

This shows how EASY it is to add noise suppression to ANY existing function
with just 1-2 lines of code!
"""

import numpy as np
import sounddevice as sd
from utils.audio_utils import apply_noise_suppression, enhanced_vad_check

# ========== EXAMPLE 1: Add to existing simple recording function ==========

def old_simple_recording(duration=3.0, sample_rate=16000):
    """OLD function without noise suppression"""
    print("🎤 Recording (OLD WAY - no noise suppression)...")
    audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
    sd.wait()
    
    # Simple energy check
    audio_samples = audio.flatten()
    energy = np.mean(audio_samples ** 2)
    has_voice = energy > 1000  # Basic threshold
    
    print(f"Energy: {energy:.0f}, Has voice: {has_voice}")
    return audio_samples, has_voice

def new_enhanced_recording(duration=3.0, sample_rate=16000):
    """NEW function with noise suppression - ONLY 2 LINES ADDED!"""
    print("🎤 Recording (NEW WAY - with noise suppression)...")
    audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
    sd.wait()
    
    audio_samples = audio.flatten()
    
    # 🎯 ADD NOISE SUPPRESSION: Just 1 line!
    enhanced_samples = apply_noise_suppression(audio_samples, sample_rate)
    
    # 🎯 ADD ENHANCED VAD: Just 1 line!
    vad_result = enhanced_vad_check(enhanced_samples, sample_rate)
    
    print(f"Energy: {vad_result['energy']:.0f}, Has voice: {vad_result['has_voice']}, "
          f"ZCR: {vad_result['zero_crossing_rate']:.3f}")
    return enhanced_samples, vad_result['has_voice']

# ========== EXAMPLE 2: Add to existing VAD function ==========

def old_simple_vad(audio_samples, threshold=0.01):
    """OLD VAD function"""
    energy = np.mean(audio_samples ** 2)
    return energy > threshold

def new_enhanced_vad(audio_samples, threshold=0.01):
    """NEW VAD function - ONLY 1 LINE ADDED!"""
    # 🎯 ADD ENHANCED VAD: Just 1 line!
    vad_result = enhanced_vad_check(audio_samples, apply_noise_suppression=True)
    return vad_result['has_voice']

# ========== EXAMPLE 3: Add to existing interrupt detection ==========

def old_interrupt_check(audio_chunk):
    """OLD interrupt detection"""
    samples = np.frombuffer(audio_chunk, dtype=np.int16)
    rms = np.sqrt(np.mean(samples ** 2))
    return rms > 800  # Simple threshold

def new_interrupt_check(audio_chunk):
    """NEW interrupt detection - ONLY 1 LINE ADDED!"""
    samples = np.frombuffer(audio_chunk, dtype=np.int16)
    
    # 🎯 ADD ENHANCED VAD: Just 1 line!
    vad_result = enhanced_vad_check(samples, apply_noise_suppression=True)
    return vad_result['has_voice']

# ========== EXAMPLE 4: Flexible noise suppression options ==========

def flexible_audio_processing(audio_samples, mode="full"):
    """Show different levels of enhancement"""
    
    if mode == "basic":
        # Just bandpass filter
        enhanced = apply_noise_suppression(
            audio_samples, 
            enable_bandpass=True, 
            enable_noise_reduction=False
        )
        
    elif mode == "noise_only":
        # Just noise reduction
        enhanced = apply_noise_suppression(
            audio_samples, 
            enable_bandpass=False, 
            enable_noise_reduction=True
        )
        
    elif mode == "full":
        # Full enhancement (default)
        enhanced = apply_noise_suppression(audio_samples)
        
    elif mode == "custom":
        # Custom settings
        enhanced = apply_noise_suppression(
            audio_samples,
            bandpass_low=100.0,      # Custom frequency range
            bandpass_high=6000.0,
            noise_strength=0.5       # Lighter noise reduction
        )
    
    return enhanced

# ========== USAGE EXAMPLES ==========

if __name__ == "__main__":
    print("🎯 MODULAR NOISE SUPPRESSION EXAMPLES")
    print("=====================================")
    
    # Example: Record and compare old vs new
    print("\n1. Recording comparison:")
    
    # Old way
    old_audio, old_result = old_simple_recording(duration=2.0)
    
    # New way (enhanced)
    new_audio, new_result = new_enhanced_recording(duration=2.0)
    
    print(f"Old result: {old_result}")
    print(f"New result: {new_result}")
    
    # Example: VAD comparison
    print("\n2. VAD comparison:")
    test_samples = np.random.randint(-1000, 1000, 16000, dtype=np.int16)  # 1 second of noise
    
    old_vad = old_simple_vad(test_samples)
    new_vad = new_enhanced_vad(test_samples)
    
    print(f"Old VAD: {old_vad}")
    print(f"New VAD: {new_vad}")
    
    print("\n✅ See how easy it is to add noise suppression to ANY function!")
