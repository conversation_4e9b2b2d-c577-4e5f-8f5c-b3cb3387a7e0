"""
🎯 COMPREHENSIVE NOISE SUPPRESSION TEST
=======================================

Test the simplified noise suppression system thoroughly.
"""

import asyncio
import numpy as np
import time
import sounddevice as sd
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)

from core.audio.noise_suppressor import get_noise_suppressor
from utils.audio_utils import enhanced_vad_check
from core.interruption.interrupt_handler import InterruptHandler


def create_test_audio_signals():
    """Create different types of test audio signals"""
    sample_rate = 16000
    duration = 1.0  # 1 second
    t = np.linspace(0, duration, int(sample_rate * duration))

    # 1. Pure noise (should be filtered out)
    noise = np.random.normal(0, 0.3, len(t))
    noise_audio = (noise * 16000).astype(np.int16)

    # 2. Human speech simulation (440Hz + harmonics + noise)
    speech_fundamental = np.sin(2 * np.pi * 440 * t)  # 440 Hz
    speech_harmonic1 = 0.5 * np.sin(2 * np.pi * 880 * t)  # 880 Hz
    speech_harmonic2 = 0.3 * np.sin(2 * np.pi * 1320 * t)  # 1320 Hz
    speech_noise = 0.1 * np.random.normal(0, 1, len(t))

    speech_signal = speech_fundamental + speech_harmonic1 + speech_harmonic2 + speech_noise
    speech_audio = (speech_signal * 8000).astype(np.int16)

    # 3. High frequency noise (should be filtered out)
    high_freq_noise = np.sin(2 * np.pi * 10000 * t)  # 10kHz
    high_freq_audio = (high_freq_noise * 8000).astype(np.int16)

    # 4. Low frequency noise (should be filtered out)
    low_freq_noise = np.sin(2 * np.pi * 50 * t)  # 50Hz
    low_freq_audio = (low_freq_noise * 8000).astype(np.int16)

    return {
        "noise": noise_audio,
        "speech": speech_audio,
        "high_freq": high_freq_audio,
        "low_freq": low_freq_audio
    }


async def test_configuration_loading():
    """Test that configuration is loaded from workflow JSON"""
    print("🎯 TEST 1: Configuration Loading")
    print("=" * 50)

    try:
        from core.config.interrupt_config import get_interrupt_config

        # Get config
        config = get_interrupt_config()

        if config:
            print("✅ Config loaded successfully")
            print(f"   📋 Noise reduction enabled: {config.global_settings.enable_noise_reduction}")
            print(f"   📋 Noise strength: {config.global_settings.noise_reduction_strength}")
            print(f"   📋 Bandpass low: {config.global_settings.bandpass_low_freq}Hz")
            print(f"   📋 Bandpass high: {config.global_settings.bandpass_high_freq}Hz")
            print(f"   📋 Min speech energy ratio: {config.global_settings.min_speech_energy_ratio}")
            print(f"   📋 Max zero crossing rate: {config.global_settings.max_zero_crossing_rate}")

            # Verify noise suppressor uses this config
            noise_suppressor = get_noise_suppressor("config_test")
            print(f"   🔧 Noise suppressor enabled: {noise_suppressor.config.enabled}")
            print(f"   🔧 Matches config: {'✅' if noise_suppressor.config.enabled == config.global_settings.enable_noise_reduction else '❌'}")

            return True
        else:
            print("❌ Config not loaded")
            return False

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

    finally:
        print()


async def test_noise_suppressor_module():
    """Test the core noise suppressor module"""
    print("🎯 TEST 2: Noise Suppressor Module")
    print("=" * 50)

    try:
        # Get noise suppressor
        noise_suppressor = get_noise_suppressor("module_test")

        # Create test signals
        test_signals = create_test_audio_signals()
        sample_rate = 16000

        results = {}

        for signal_name, audio_samples in test_signals.items():
            print(f"   🧪 Testing {signal_name} signal...")

            # Test sync processing
            start_time = time.time()
            filtered_sync, info_sync = noise_suppressor.process_audio_sync(audio_samples, sample_rate)
            sync_time = (time.time() - start_time) * 1000

            # Test async processing
            start_time = time.time()
            filtered_async, info_async = await noise_suppressor.process_audio_async(audio_samples, sample_rate)
            async_time = (time.time() - start_time) * 1000

            # Calculate energy reduction
            original_energy = np.mean(audio_samples.astype(np.float64) ** 2)
            filtered_energy = np.mean(filtered_sync.astype(np.float64) ** 2)
            energy_reduction = ((original_energy - filtered_energy) / original_energy) * 100

            results[signal_name] = {
                "original_energy": original_energy,
                "filtered_energy": filtered_energy,
                "energy_reduction_percent": energy_reduction,
                "sync_processing_time": sync_time,
                "async_processing_time": async_time,
                "noise_suppression_applied": info_sync.get('noise_suppression_applied', False)
            }

            print(f"      📊 Original energy: {original_energy:.0f}")
            print(f"      📊 Filtered energy: {filtered_energy:.0f}")
            print(f"      📊 Energy reduction: {energy_reduction:.1f}%")
            print(f"      ⏱️ Sync time: {sync_time:.1f}ms")
            print(f"      ⏱️ Async time: {async_time:.1f}ms")
            print(f"      🔧 Suppression applied: {info_sync.get('noise_suppression_applied', False)}")
            print()

        # Verify results make sense
        print("   🔍 Analysis:")

        # Speech should have less energy reduction than pure noise
        if results["speech"]["energy_reduction_percent"] < results["noise"]["energy_reduction_percent"]:
            print("   ✅ Speech preserved better than noise (good!)")
        else:
            print("   ⚠️ Speech not preserved better than noise")

        # High/low frequency noise should be heavily reduced
        if results["high_freq"]["energy_reduction_percent"] > 50:
            print("   ✅ High frequency noise heavily reduced (good!)")
        else:
            print("   ⚠️ High frequency noise not sufficiently reduced")

        if results["low_freq"]["energy_reduction_percent"] > 50:
            print("   ✅ Low frequency noise heavily reduced (good!)")
        else:
            print("   ⚠️ Low frequency noise not sufficiently reduced")

        # Processing should be fast (< 50ms for real-time)
        avg_async_time = np.mean([r["async_processing_time"] for r in results.values()])
        if avg_async_time < 50:
            print(f"   ✅ Fast processing: {avg_async_time:.1f}ms average (good for real-time!)")
        else:
            print(f"   ⚠️ Slow processing: {avg_async_time:.1f}ms average (may affect real-time)")

        return True

    except Exception as e:
        print(f"❌ Noise suppressor module test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        print()


async def test_vad_integration():
    """Test VAD integration with noise suppressor"""
    print("🎯 TEST 3: VAD Integration")
    print("=" * 50)

    try:
        # Get noise suppressor
        noise_suppressor = get_noise_suppressor("vad_test")

        # Create test signals
        test_signals = create_test_audio_signals()
        sample_rate = 16000

        for signal_name, audio_samples in test_signals.items():
            print(f"   🧪 Testing VAD on {signal_name}...")

            # Process through noise suppressor first
            filtered_audio, _ = await noise_suppressor.process_audio_async(audio_samples, sample_rate)

            # Test VAD on original vs filtered
            vad_original = enhanced_vad_check(audio_samples, sample_rate)
            vad_filtered = enhanced_vad_check(filtered_audio, sample_rate)

            print(f"      📊 Original VAD: {vad_original.get('has_voice', False)} (energy: {vad_original.get('energy', 0):.0f})")
            print(f"      📊 Filtered VAD: {vad_filtered.get('has_voice', False)} (energy: {vad_filtered.get('energy', 0):.0f})")
            print(f"      📊 ZCR: {vad_filtered.get('zero_crossing_rate', 0):.3f}")
            print(f"      📊 Spectral centroid: {vad_filtered.get('spectral_centroid', 0):.1f}Hz")
            print()

        print("   🔍 Expected behavior:")
        print("      ✅ Speech signal should trigger VAD")
        print("      ✅ Noise signals should NOT trigger VAD after filtering")
        print("      ✅ Filtered audio should have cleaner metrics")

        return True

    except Exception as e:
        print(f"❌ VAD integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        print()


async def test_interrupt_handler_integration():
    """Test interrupt handler integration"""
    print("🎯 TEST 4: Interrupt Handler Integration")
    print("=" * 50)

    try:
        # Create a mock memory manager
        class MockMemoryManager:
            def get(self, key):
                return None
            def set(self, key, value):
                pass

        # Create interrupt handler
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("test_session", memory_manager)

        print("   ✅ InterruptHandler created successfully")
        print(f"   🔧 Has noise suppressor: {hasattr(interrupt_handler, 'noise_suppressor')}")

        if hasattr(interrupt_handler, 'noise_suppressor'):
            ns = interrupt_handler.noise_suppressor
            print(f"   🔧 Noise suppressor enabled: {ns.config.enabled}")
            print(f"   🔧 Noise strength: {ns.config.noise_strength}")
            print(f"   🔧 Bandpass range: {ns.config.bandpass_low}-{ns.config.bandpass_high}Hz")

        return True

    except Exception as e:
        print(f"❌ Interrupt handler integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        print()


async def test_real_microphone_recording():
    """Test with real microphone (if available)"""
    print("🎯 TEST 5: Real Microphone Recording")
    print("=" * 50)

    try:
        # Check if microphone is available
        devices = sd.query_devices()
        input_devices = [d for d in devices if d['max_input_channels'] > 0]

        if not input_devices:
            print("   ⚠️ No microphone detected, skipping real recording test")
            return True

        print("   🎤 Microphone detected, testing real recording...")
        print("   📢 Please speak for 2 seconds when prompted...")

        # Wait a moment
        await asyncio.sleep(1)
        print("   🔴 Recording NOW! Say something...")

        # Record 2 seconds
        sample_rate = 16000
        duration = 2.0
        audio = sd.rec(int(duration * sample_rate), samplerate=sample_rate, channels=1, dtype='int16')
        sd.wait()

        audio_samples = audio.flatten()
        print("   ✅ Recording completed")

        # Process through noise suppressor
        noise_suppressor = get_noise_suppressor("mic_test")
        filtered_audio, info = await noise_suppressor.process_audio_async(audio_samples, sample_rate)

        # Test VAD on both
        vad_original = enhanced_vad_check(audio_samples, sample_rate)
        vad_filtered = enhanced_vad_check(filtered_audio, sample_rate)

        print(f"   📊 Processing time: {info.get('processing_time_ms', 0):.1f}ms")
        print(f"   📊 Original energy: {vad_original.get('energy', 0):.0f}")
        print(f"   📊 Filtered energy: {vad_filtered.get('energy', 0):.0f}")
        print(f"   📊 Original VAD: {vad_original.get('has_voice', False)}")
        print(f"   📊 Filtered VAD: {vad_filtered.get('has_voice', False)}")

        # Calculate noise reduction
        energy_reduction = ((vad_original.get('energy', 0) - vad_filtered.get('energy', 0)) / vad_original.get('energy', 1)) * 100
        print(f"   📊 Energy reduction: {energy_reduction:.1f}%")

        return True

    except Exception as e:
        print(f"   ⚠️ Real microphone test failed (this is often normal): {e}")
        return True  # Don't fail the whole test suite for mic issues

    finally:
        print()


async def test_performance_under_load():
    """Test performance under load"""
    print("🎯 TEST 6: Performance Under Load")
    print("=" * 50)

    try:
        noise_suppressor = get_noise_suppressor("load_test")
        sample_rate = 16000
        chunk_size = int(0.03 * sample_rate)  # 30ms chunks
        num_chunks = 100  # 3 seconds of audio

        print(f"   🧪 Processing {num_chunks} chunks ({num_chunks * 30}ms of audio)...")

        processing_times = []

        for i in range(num_chunks):
            # Generate random audio chunk
            test_chunk = np.random.randint(-1000, 1000, chunk_size, dtype=np.int16)

            # Process asynchronously
            start_time = time.time()
            _, info = await noise_suppressor.process_audio_async(test_chunk, sample_rate)
            processing_time = (time.time() - start_time) * 1000

            processing_times.append(processing_time)

            # Progress indicator
            if (i + 1) % 20 == 0:
                print(f"      Processed {i + 1}/{num_chunks} chunks...")

        # Calculate statistics
        avg_time = np.mean(processing_times)
        min_time = np.min(processing_times)
        max_time = np.max(processing_times)
        p95_time = np.percentile(processing_times, 95)

        print(f"   📊 Average processing time: {avg_time:.1f}ms")
        print(f"   📊 Min processing time: {min_time:.1f}ms")
        print(f"   📊 Max processing time: {max_time:.1f}ms")
        print(f"   📊 95th percentile: {p95_time:.1f}ms")

        # Performance evaluation
        if avg_time < 30:
            print("   ✅ Excellent performance for real-time processing!")
        elif avg_time < 50:
            print("   ✅ Good performance for real-time processing")
        else:
            print("   ⚠️ Performance may be too slow for real-time processing")

        return True

    except Exception as e:
        print(f"❌ Performance test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

    finally:
        print()


async def main():
    """Run all comprehensive tests"""
    print("🎯 COMPREHENSIVE NOISE SUPPRESSION TESTS")
    print("=" * 60)
    print("Testing the simplified noise suppression system...")
    print()

    test_results = []

    # Run all tests
    tests = [
        ("Configuration Loading", test_configuration_loading),
        ("Noise Suppressor Module", test_noise_suppressor_module),
        ("VAD Integration", test_vad_integration),
        ("Interrupt Handler Integration", test_interrupt_handler_integration),
        ("Real Microphone Recording", test_real_microphone_recording),
        ("Performance Under Load", test_performance_under_load)
    ]

    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            test_results.append((test_name, False))

    # Summary
    print("🎯 TEST SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
        if result:
            passed += 1

    print()
    print(f"📊 RESULTS: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")

    if passed == total:
        print("🎉 ALL TESTS PASSED! The noise suppression system is working correctly!")
    else:
        print("⚠️ Some tests failed. Please check the output above for details.")

    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
