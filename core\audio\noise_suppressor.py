"""
🎯 NOISE SUPPRESSOR MODULE
==========================

Simple, dedicated module that sits between raw audio input and VAD pipeline.
All downstream processing uses the filtered version.

Configuration is read from workflow JSON file.
"""

import asyncio
import time
from concurrent.futures import ThreadPoolExecutor
from typing import Dict, Any, Optional, Tuple
import numpy as np

try:
    import noisereduce as nr
    _noisereduce_available = True
except ImportError:
    _noisereduce_available = False

from scipy.signal import butter, lfilter
from core.logging.logger_config import get_module_logger
from core.config.interrupt_config import get_interrupt_config

logger = get_module_logger("noise_suppressor")


class NoiseSuppressionConfig:
    """Simple configuration for noise suppression - reads from workflow JSON"""

    def __init__(self):
        config = get_interrupt_config()

        if config:
            self.enabled = config.global_settings.enable_noise_reduction
            self.noise_strength = config.global_settings.noise_reduction_strength
            self.bandpass_low = config.global_settings.bandpass_low_freq
            self.bandpass_high = config.global_settings.bandpass_high_freq
        else:
            # Default values
            self.enabled = True
            self.noise_strength = 0.7
            self.bandpass_low = 85.0
            self.bandpass_high = 8000.0


class NoiseSuppressor:
    """
    🎯 SIMPLE NOISE SUPPRESSOR MODULE

    Sits between raw audio input and all downstream processing.
    Configuration comes from workflow JSON file.
    """

    def __init__(self, session_id: str = None):
        self.session_id = session_id or "default"
        self.config = NoiseSuppressionConfig()
        self.logger = get_module_logger("noise_suppressor", session_id=self.session_id)

        # Threading support for async processing
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="noise_suppressor")
    
    def process_audio_sync(self, audio_samples: np.ndarray, sample_rate: int = 16000) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        🎯 PROCESS AUDIO WITH NOISE SUPPRESSION

        Args:
            audio_samples: Raw audio samples
            sample_rate: Sample rate (default: 16000)

        Returns:
            Tuple of (processed_audio, processing_info)
        """
        start_time = time.time()

        try:
            if not self.config.enabled:
                # Noise suppression disabled in workflow config - return original audio
                return audio_samples, {"noise_suppression_applied": False}

            processed_samples = audio_samples.copy().astype(np.float64)

            # Step 1: Bandpass filter (human speech frequencies)
            processed_samples = self._apply_bandpass_filter(processed_samples, sample_rate)

            # Step 2: Noise reduction
            if _noisereduce_available:
                processed_samples = self._apply_noise_reduction(processed_samples, sample_rate)

            # Convert back to original dtype
            processed_samples = processed_samples.astype(audio_samples.dtype)

            processing_time = (time.time() - start_time) * 1000  # ms

            processing_info = {
                "noise_suppression_applied": True,
                "processing_time_ms": processing_time,
                "noisereduce_available": _noisereduce_available
            }

            return processed_samples, processing_info

        except Exception as e:
            self.logger.error(f"Error in noise suppression: {e}")
            # Return original audio on error
            return audio_samples, {"noise_suppression_applied": False, "error": str(e)}
    
    async def process_audio_async(self, audio_samples: np.ndarray, sample_rate: int = 16000) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        🎯 ASYNC AUDIO PROCESSING (THREADED FOR PERFORMANCE)

        Args:
            audio_samples: Raw audio samples
            sample_rate: Sample rate (default: 16000)

        Returns:
            Tuple of (processed_audio, processing_info)
        """
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.executor,
            self.process_audio_sync,
            audio_samples,
            sample_rate
        )
    
    def _apply_bandpass_filter(self, samples: np.ndarray, sample_rate: int) -> np.ndarray:
        """Apply bandpass filter for human speech frequencies"""
        try:
            nyquist = sample_rate / 2.0
            low_norm = max(1.0, self.config.bandpass_low) / nyquist
            high_norm = min(self.config.bandpass_high, nyquist - 1) / nyquist
            
            if low_norm >= high_norm:
                self.logger.warning("Invalid bandpass frequencies, skipping filter")
                return samples
            
            # 4th order Butterworth bandpass filter
            b, a = butter(4, [low_norm, high_norm], btype='band')
            filtered_samples = lfilter(b, a, samples)
            
            return filtered_samples
            
        except Exception as e:
            self.logger.warning(f"Bandpass filter failed: {e}")
            return samples
    
    def _apply_noise_reduction(self, samples: np.ndarray, sample_rate: int) -> np.ndarray:
        """Apply noise reduction using spectral gating"""
        try:
            # Convert to float32 for noisereduce
            float_samples = samples.astype(np.float32)
            
            # Apply noise reduction
            reduced_samples = nr.reduce_noise(
                y=float_samples,
                sr=sample_rate,
                stationary=True,
                prop_decrease=self.config.noise_strength
            )
            
            return reduced_samples.astype(samples.dtype)
            
        except Exception as e:
            self.logger.warning(f"Noise reduction failed: {e}")
            return samples
    
    def cleanup(self):
        """Cleanup resources"""
        self.executor.shutdown(wait=True)


# ========== SIMPLE GLOBAL INSTANCE ==========

_global_noise_suppressor: Optional[NoiseSuppressor] = None

def get_noise_suppressor(session_id: str = None) -> NoiseSuppressor:
    """Get global noise suppressor instance"""
    global _global_noise_suppressor

    if _global_noise_suppressor is None:
        _global_noise_suppressor = NoiseSuppressor(session_id)

    return _global_noise_suppressor
