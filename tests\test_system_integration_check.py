"""
🎯 SYSTEM INTEGRATION CHECK
===========================

Comprehensive test to verify all components are properly integrated and connected.
"""

import asyncio
import numpy as np
from pathlib import Path
import sys

# Add project root to path
project_root = str(Path(__file__).parent.parent)
sys.path.append(project_root)


async def test_all_integrations():
    """Test all system integrations"""
    print("🎯 SYSTEM INTEGRATION CHECK")
    print("=" * 60)
    
    issues_found = []
    
    # Test 1: Configuration Loading
    print("1️⃣ Testing Configuration Loading...")
    try:
        from core.config.interrupt_config import get_interrupt_config
        config = get_interrupt_config()
        
        if config:
            print(f"   ✅ Config loaded: VAD threshold = {config.global_settings.vad_threshold}")
            print(f"   ✅ Noise reduction: {config.global_settings.enable_noise_reduction}")
        else:
            issues_found.append("❌ Configuration not loaded")
            
    except Exception as e:
        issues_found.append(f"❌ Config loading failed: {e}")
    
    # Test 2: Noise Suppressor Integration
    print("\n2️⃣ Testing Noise Suppressor Integration...")
    try:
        from core.audio.noise_suppressor import get_noise_suppressor
        
        noise_suppressor = get_noise_suppressor("integration_test")
        
        if noise_suppressor:
            print(f"   ✅ Noise suppressor created")
            print(f"   ✅ Config loaded: {noise_suppressor.config.enabled}")
            
            # Test processing
            import numpy as np  # Ensure numpy is available in this scope
            test_audio = np.random.randint(-1000, 1000, 1600, dtype=np.int16)
            filtered, info = await noise_suppressor.process_audio_async(test_audio, 16000)
            
            if len(filtered) == len(test_audio):
                print(f"   ✅ Audio processing works: {info.get('processing_time_ms', 0):.1f}ms")
            else:
                issues_found.append("❌ Audio processing output size mismatch")
        else:
            issues_found.append("❌ Noise suppressor not created")
            
    except Exception as e:
        issues_found.append(f"❌ Noise suppressor failed: {e}")
    
    # Test 3: Interrupt Handler Integration
    print("\n3️⃣ Testing Interrupt Handler Integration...")
    try:
        from core.interruption.interrupt_handler import InterruptHandler
        
        class MockMemoryManager:
            def get(self, key): return None
            def set(self, key, value): pass
        
        memory_manager = MockMemoryManager()
        interrupt_handler = InterruptHandler("integration_test", memory_manager)
        
        if interrupt_handler:
            print(f"   ✅ Interrupt handler created")
            
            # Test interrupt-specific VAD
            import numpy as np  # Ensure numpy is available in this scope
            test_audio = np.random.randint(-1000, 1000, 1600, dtype=np.int16)
            vad_result = interrupt_handler._interrupt_specific_vad(test_audio, 16000, 50.0)
            
            if 'has_voice' in vad_result:
                print(f"   ✅ Interrupt VAD works: {vad_result['has_voice']}")
            else:
                issues_found.append("❌ Interrupt VAD missing 'has_voice' key")
                
            # Check noise suppressor integration
            if hasattr(interrupt_handler, 'noise_suppressor'):
                print(f"   ✅ Noise suppressor integrated")
            else:
                issues_found.append("❌ Noise suppressor not integrated in interrupt handler")
        else:
            issues_found.append("❌ Interrupt handler not created")
            
    except Exception as e:
        issues_found.append(f"❌ Interrupt handler failed: {e}")
    
    # Test 4: Audio Utils Integration
    print("\n4️⃣ Testing Audio Utils Integration...")
    try:
        from utils.audio_utils import enhanced_vad_check
        
        import numpy as np  # Ensure numpy is available in this scope
        test_audio = np.random.randint(-1000, 1000, 1600, dtype=np.int16)
        vad_result = enhanced_vad_check(test_audio, 16000, 50.0)
        
        if 'has_voice' in vad_result:
            print(f"   ✅ Enhanced VAD works: {vad_result['has_voice']}")
            print(f"   ✅ Energy: {vad_result.get('energy', 0):.0f}")
        else:
            issues_found.append("❌ Enhanced VAD missing 'has_voice' key")
            
    except Exception as e:
        issues_found.append(f"❌ Audio utils failed: {e}")
    
    # Test 5: Recording Function Integration
    print("\n5️⃣ Testing Recording Function Integration...")
    try:
        from utils.audio_utils import record_microphone_audio_vad
        
        # Just check if the function exists and can be imported
        if callable(record_microphone_audio_vad):
            print(f"   ✅ Recording function available")
            
            # Check if it properly imports noise suppressor
            import inspect
            source = inspect.getsource(record_microphone_audio_vad)
            if 'get_noise_suppressor' in source:
                print(f"   ✅ Recording function uses noise suppressor")
            else:
                issues_found.append("❌ Recording function doesn't use noise suppressor")
        else:
            issues_found.append("❌ Recording function not callable")
            
    except Exception as e:
        issues_found.append(f"❌ Recording function failed: {e}")
    
    # Test 6: Configuration Consistency
    print("\n6️⃣ Testing Configuration Consistency...")
    try:
        from core.config.interrupt_config import get_interrupt_config
        from core.audio.noise_suppressor import get_noise_suppressor
        
        config = get_interrupt_config()
        noise_suppressor = get_noise_suppressor("consistency_test")
        
        # Check if thresholds match
        config_threshold = config.global_settings.vad_threshold
        config_noise_enabled = config.global_settings.enable_noise_reduction
        ns_enabled = noise_suppressor.config.enabled
        
        if config_noise_enabled == ns_enabled:
            print(f"   ✅ Noise reduction settings consistent: {config_noise_enabled}")
        else:
            issues_found.append(f"❌ Noise reduction mismatch: config={config_noise_enabled}, ns={ns_enabled}")
        
        print(f"   ✅ VAD threshold: {config_threshold}")
        
    except Exception as e:
        issues_found.append(f"❌ Configuration consistency check failed: {e}")
    
    # Test 7: Import Dependencies
    print("\n7️⃣ Testing Import Dependencies...")
    try:
        import numpy as np
        import sounddevice as sd
        import asyncio
        import json
        print(f"   ✅ Core dependencies available")
        
        # Check optional dependencies
        try:
            import scipy
            print(f"   ✅ SciPy available")
        except ImportError:
            print(f"   ⚠️ SciPy not available (optional)")
            
    except Exception as e:
        issues_found.append(f"❌ Import dependencies failed: {e}")
    
    # Summary
    print("\n🎯 INTEGRATION CHECK SUMMARY")
    print("=" * 60)
    
    if not issues_found:
        print("🎉 ALL INTEGRATIONS WORKING PERFECTLY!")
        print("✅ Configuration loading")
        print("✅ Noise suppressor integration")
        print("✅ Interrupt handler integration")
        print("✅ Audio utils integration")
        print("✅ Recording function integration")
        print("✅ Configuration consistency")
        print("✅ Import dependencies")
        print("\n🚀 SYSTEM IS READY FOR PRODUCTION!")
        
    else:
        print(f"⚠️ FOUND {len(issues_found)} INTEGRATION ISSUES:")
        for issue in issues_found:
            print(f"   {issue}")
        print("\n🔧 Please fix these issues before production use.")
    
    print("=" * 60)
    return len(issues_found) == 0


async def main():
    """Run integration check"""
    try:
        success = await test_all_integrations()
        if success:
            print("\n✅ System integration check completed successfully!")
        else:
            print("\n❌ System integration check found issues!")
    except Exception as e:
        print(f"❌ Integration check failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
